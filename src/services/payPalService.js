import axios from "axios";
import qs from "qs";
import { getCache, setCache } from "../config/cache.js";
import paypalConfig from "../config/paypal.json" with { type: "json" };
import dotenv from "dotenv";
import { sendPayPalErrorAlert } from './telegramService.js';
import logger from "../utils/logger.js";

dotenv.config();

const PAYPAL_API_URL = process.env.PAYPAL_API_URL;
const PAYPAL_CLIENT_ID = process.env.PAYPAL_CLIENT_ID;
const PAYPAL_SECRET = process.env.PAYPAL_SECRET;

const baseURL = PAYPAL_API_URL || "https://api-m.sandbox.paypal.com";
const isLocal = process.env.NODE_ENV === "local";

function shouldHideProductName(paypal_client_id) {
  const paypalConfigEntry = Object.values(paypalConfig).find(
    config => config.client_id === paypal_client_id || config.client_id_2 === paypal_client_id
  );

  return paypalConfigEntry ? paypalConfigEntry.hidden_product_name : false;
}

async function getPayPalConfig(clientId) {
  if (isLocal) {
    return {
      client_id: PAYPAL_CLIENT_ID,
      secret_key: PAYPAL_SECRET,
      currency_code: "USD" // Default currency for local development
    };
  }

  for (const [port, config] of Object.entries(paypalConfig)) {
    if (config.client_id === clientId) {
      return {
        ...config,
        currency_code: config.currency_code || "USD" // Use configured currency or default to USD
      };
    }
  }
  throw new Error(`No configuration found for client ID: ${clientId}`);
}

// Redis-based distributed lock để tránh race condition giữa các processes

export async function getAccessToken(clientId) {
  try {
    const cacheKey = `paypal_access_token_${clientId}`;
    const lockKey = `paypal_token_lock_${clientId}`;

    // Try to get the token from cache first
    const cachedToken = await getCache(cacheKey);
    if (cachedToken) {
      console.log("Using cached token for", clientId);
      return cachedToken;
    }

    // Check if another process is already creating token
    const existingLock = await getCache(lockKey);
    if (existingLock) {
      console.log(`Token creation in progress for ${clientId}, waiting...`);
      // Wait a bit and try cache again
      await new Promise(resolve => setTimeout(resolve, 100 + Math.random() * 200));
      const retryToken = await getCache(cacheKey);
      if (retryToken) {
        console.log(`Found token after waiting for ${clientId}`);
        return retryToken;
      }
    }

    // Set lock to prevent other processes from creating token
    await setCache(lockKey, process.pid, [], 30); // 30 seconds lock

    try {
      // Double-check cache after acquiring lock
      const doubleCheckToken = await getCache(cacheKey);
      if (doubleCheckToken) {
        console.log("Token found after acquiring lock for", clientId);
        await setCache(lockKey, null); // Release lock
        return doubleCheckToken;
      }

      const config = await getPayPalConfig(clientId);
      console.log("Creating new PayPal access token for", clientId, "by process", process.pid);

      const data = qs.stringify({
        grant_type: "client_credentials",
      });

      const startTime = Date.now();
      const response = await axios.post(`${baseURL}/v1/oauth2/token`, data, {
        headers: {
          "Content-Type": "application/x-www-form-urlencoded",
        },
        auth: {
          username: config.client_id,
          password: config.secret_key,
        },
        timeout: 10000, // 10 second timeout
      });

      const accessToken = response.data.access_token;
      const duration = Date.now() - startTime;

      console.log(`PayPal token created successfully for ${clientId} in ${duration}ms by process ${process.pid}`);

      // Cache the new token with longer TTL to reduce API calls
      await setCache(cacheKey, accessToken, ["paypal_tokens"], 2 * 3600);

      // Release lock
      await setCache(lockKey, null);

      return accessToken;
    } catch (error) {
      // Release lock on error
      await setCache(lockKey, null);
      throw error;
    }
  } catch (error) {
    console.error(
      "Error getting PayPal access token:",
      error.response ? error.response.data : error.message,
    );
    throw error;
  }
}

export const createPayPalOrder = async (clientId, orderData, retryCount = 0) => {
  const startTime = Date.now();
  try {
    const tokenStartTime = Date.now();
    const accessToken = await getAccessToken(clientId);
    const tokenDuration = Date.now() - tokenStartTime;

    if (tokenDuration > 1000) {
      console.warn(`Slow token retrieval for ${clientId}: ${tokenDuration}ms`);
    }
    
    const hideProductName = shouldHideProductName(
      orderData.paypal_client_id || clientId
    );
    const subTotal = parseFloat(orderData.sub_total) || 0;
    const shippingTotal = parseFloat(orderData.shipping_total) || 0;
    const discountTotal = parseFloat(orderData.discount_total) || 0;
    const tipTotal = parseFloat(orderData.tip_total) || 0;
    const currency = orderData.currency || "USD";

    const calculatedTotal = subTotal + shippingTotal - discountTotal + tipTotal;
    const providedTotal = parseFloat(orderData.total) || 0;

    if (Math.abs(calculatedTotal - providedTotal) > 0.01) {
      console.error('Total mismatch:', {
        clientId,
        calculated: calculatedTotal,
        provided: providedTotal,
        difference: calculatedTotal - providedTotal
      });
      throw new Error('Order total does not match breakdown components');
    }

    const paypalOrderData = {
      intent: "CAPTURE",
      purchase_units: [
        {
          amount: {
            currency_code: currency,
            value: providedTotal.toFixed(2),
            breakdown: {
              item_total: {
                currency_code: currency,
                value: subTotal.toFixed(2),
              },
              shipping: {
                currency_code: currency,
                value: shippingTotal.toFixed(2),
              },
            },
          },
          items: orderData.line_items.map((item) => ({
            name: (hideProductName ? (item.sku || item.name) : item.name).substring(0, 126),
            quantity: String(item.quantity),
            unit_amount: {
              currency_code: currency,
              value: String(item.price),
            },
            sku: String(item.sku),
          })),
          invoice_id: orderData.invoice_id,
          custom_id: orderData.funding_source,
        },
      ],
    };

    if (discountTotal > 0) {
      paypalOrderData.purchase_units[0].amount.breakdown.discount = {
        currency_code: currency,
        value: discountTotal.toFixed(2),
      };
    }

    if (tipTotal > 0) {
      paypalOrderData.purchase_units[0].amount.breakdown.handling = {
        currency_code: currency,
        value: tipTotal.toFixed(2),
      };
    }

    const orderStartTime = Date.now();
    const response = await axios.post(
      `${baseURL}/v2/checkout/orders`,
      paypalOrderData,
      {
        headers: {
          "Content-Type": "application/json",
          Authorization: `Bearer ${accessToken}`,
        },
        timeout: 15000, // 15 second timeout
      },
    );

    const totalDuration = Date.now() - startTime;
    const orderDuration = Date.now() - orderStartTime;

    console.log(`PayPal order created successfully for ${clientId}`, {
      totalDuration: `${totalDuration}ms`,
      tokenDuration: `${tokenDuration}ms`,
      orderDuration: `${orderDuration}ms`,
      orderId: response.data.id,
      process: process.pid
    });

    return response.data;
  } catch (error) {
    if (error.response?.status === 401 && retryCount < 1) {
      console.log('Access token expired for createPayPalOrder, retrying with new token...', { clientId, process: process.pid });
      const cacheKey = `paypal_access_token_${clientId}`;

      // Add small random delay to prevent thundering herd
      await new Promise(resolve => setTimeout(resolve, 50 + Math.random() * 100));

      // Clear token cache
      await setCache(cacheKey, null);

      // Wait a bit more before retry to let other processes handle it
      await new Promise(resolve => setTimeout(resolve, 100 + Math.random() * 200));

      return createPayPalOrder(clientId, orderData, retryCount + 1);
    }

    const errorDetails = error.response?.data?.details?.[0] || {};
    console.error("PayPal Error:", {
      clientId,
      description: errorDetails.description,
      issue: errorDetails.issue,
      field: errorDetails.field,
      status: error.response?.status,
      orderInfo: orderData
    });
    
    throw error;
  }
};

export const capturePayPalOrder = async (clientId, orderId, domain, metaData = [], retryCount = 0) => {
  try {
    const accessToken = await getAccessToken(clientId);

    const response = await axios.post(
      `${baseURL}/v2/checkout/orders/${orderId}/capture`,
      {}, 
      {
        headers: {
          "Content-Type": "application/json",
          "Authorization": `Bearer ${accessToken}`,
        },
      },
    );

    logger.info('PayPal capture successful', {
      orderId,
      clientId,
      domain,
      status: response.data.status
    });

    return response.data;
  } catch (error) {
    if (error.response?.status === 401 && retryCount < 1) {
      logger.info('Access token expired for capturePayPalOrder, retrying with new token...', {
        orderId,
        clientId,
        domain,
        process: process.pid
      });
      const cacheKey = `paypal_access_token_${clientId}`;

      // Add small random delay to prevent thundering herd
      await new Promise(resolve => setTimeout(resolve, 50 + Math.random() * 100));

      // Clear token cache
      await setCache(cacheKey, null);

      // Wait a bit more before retry to let other processes handle it
      await new Promise(resolve => setTimeout(resolve, 100 + Math.random() * 200));

      return capturePayPalOrder(clientId, orderId, domain, metaData, retryCount + 1);
    }
    
    // Handle PayPal specific errors
    const paypalError = error.response?.data;
    const errorCode = paypalError?.details?.[0]?.issue;
    const errorMessage = paypalError?.message || error.message;

    // Create error object with appropriate details
    let errorDetails = {
      error: "Payment processing failed",
      details: errorMessage,
      status: error.response?.status || 500
    };

    // Map PayPal error codes to specific error details
    switch (errorCode) {
      case "INSTRUMENT_DECLINED":
      case "PAYMENT_SOURCE_DECLINED":
        errorDetails = {
          error: "Payment declined",
          details: "The payment was declined. Please try a different payment method or contact your bank.",
          code: errorCode,
          status: 400,
          canRetry: true
        };
        break;
      case "INSUFFICIENT_FUNDS":
        errorDetails = {
          error: "Insufficient funds",
          details: "There are insufficient funds in your account. Please try a different payment method.",
          code: errorCode,
          status: 400,
          canRetry: true
        };
        break;
      case "TRANSACTION_REFUSED":
        errorDetails = {
          error: "Transaction refused",
          details: "The transaction was refused. Please try again or contact support.",
          code: errorCode,
          status: 400,
          canRetry: false
        };
        break;
      case "ORDER_ALREADY_CAPTURED":
        errorDetails = {
          error: "Order already captured",
          details: "This order has already been captured.",
          code: errorCode,
          status: 400,
          canRetry: false
        };
        break;
      case "ORDER_NOT_APPROVED":
        errorDetails = {
          error: "Order not approved",
          details: "The order has not been approved for capture.",
          code: errorCode,
          status: 400,
          canRetry: true
        };
        break;
      case "ORDER_EXPIRED":
        errorDetails = {
          error: "Order expired",
          details: "The order has expired. Please create a new order.",
          code: errorCode,
          status: 400,
          canRetry: false
        };
        break;
      case "INVALID_SECURITY_CODE":
        errorDetails = {
          error: "Invalid security code",
          details: "The security code provided is invalid.",
          code: errorCode,
          status: 400,
          canRetry: true
        };
        break;
      default:
        // For unknown errors, include the original error message
        errorDetails = {
          error: "Payment processing failed",
          details: errorMessage,
          code: errorCode,
          status: error.response?.status || 500,
          canRetry: false
        };
    }

    // Log error with detailed information
    logger.error("Error capturing PayPal order:", {
      error: errorDetails,
      orderId,
      clientId,
      domain,
      status: error.response?.status,
      paypalError: paypalError,
      metaData
    });

    // Send to Telegram with detailed error information including metaData
    await sendPayPalErrorAlert(error, {
      action: 'capture',
      orderId,
      clientId,
      domain,
      retryCount,
      status: error.response?.status,
      errorDetails,
      metaData,
      timestamp: new Date().toISOString()
    });

    // Create a custom error object with the details
    const customError = new Error(errorDetails.details);
    customError.paypalError = errorDetails;
    throw customError;
  }
};

export const checkPaymentStatus = async (orderId, clientId, retryCount = 0) => {
  try {
    if (!orderId) {
      throw new Error('Order ID is required');
    }

    const accessToken = await getAccessToken(clientId);
    const response = await axios.get(
      `${baseURL}/v2/checkout/orders/${orderId}`,
      {
        headers: {
          "Content-Type": "application/json",
          Authorization: `Bearer ${accessToken}`,
        },
      }
    );

    console.log('PayPal order status checked:', {
      orderId,
      clientId,
      status: response.data.status,
      create_time: response.data.create_time,
      update_time: response.data.update_time
    });

    return response.data.status;
  } catch (error) {
    if (error.response?.status === 401 && retryCount < 1) {
      console.log('Access token expired for checkPaymentStatus, retrying with new token...', { clientId, process: process.pid });
      const cacheKey = `paypal_access_token_${clientId}`;

      // Add small random delay to prevent thundering herd
      await new Promise(resolve => setTimeout(resolve, 50 + Math.random() * 100));

      // Clear token cache
      await setCache(cacheKey, null);

      // Wait a bit more before retry to let other processes handle it
      await new Promise(resolve => setTimeout(resolve, 100 + Math.random() * 200));

      return checkPaymentStatus(orderId, clientId, retryCount + 1);
    }
    console.error("Error checking PayPal order status:", error.response?.data || error);
    throw error;
  }
};
